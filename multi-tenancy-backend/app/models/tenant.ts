import { DateTime } from 'luxon'
import { BaseModel, column, hasMany, hasOne, manyToMany } from '@adonisjs/lucid/orm'
import type { HasMany, HasOne, ManyToMany } from '@adonisjs/lucid/types/relations'
import User from '#models/user'
import Billing from './billing.js'

export enum TenantStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  TRIAL = 'trial',
}

export default class Tenant extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare name: string

  @column()
  declare slug: string

  @column()
  declare status: TenantStatus

  @column.dateTime()
  declare trialEndsAt: DateTime | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @manyToMany(() => User, {
    pivotTable: 'user_roles',
    pivotForeignKey: 'tenant_id',
    pivotRelatedForeignKey: 'user_id',
    pivotColumns: ['role_id'],
  })
  declare users: ManyToMany<typeof User>

  @hasOne(() => Billing)
  declare billing: HasOne<typeof Billing>

  // Helper methods
  get isActive() {
    return this.status === TenantStatus.ACTIVE
  }

  get isOnTrial() {
    return this.status === TenantStatus.TRIAL
  }

  get trialExpired() {
    return this.trialEndsAt && this.trialEndsAt < DateTime.now()
  }
}
