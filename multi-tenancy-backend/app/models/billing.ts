import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Tenant from './tenant.js'
import PricingPlan from './pricing_plan.js'

export enum BillingStatus {
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  CANCELLED = 'cancelled',
  PAST_DUE = 'past_due',
}

export enum BillingPlan {
  FREE = 'free',
  BASIC = 'basic',
  PRO = 'pro',
  ENTERPRISE = 'enterprise',
}

export enum BillingCycle {
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
}

export default class Billing extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare tenantId: number

  @column()
  declare plan: BillingPlan

  @column()
  declare status: BillingStatus

  @column()
  declare cycle: BillingCycle

  @column()
  declare amount: number // Amount in cents

  @column()
  declare currency: string

  @column()
  declare stripeCustomerId: string | null

  @column()
  declare stripeSubscriptionId: string | null

  @column()
  declare stripePriceId: string | null

  @column.dateTime()
  declare currentPeriodStart: DateTime | null

  @column.dateTime()
  declare currentPeriodEnd: DateTime | null

  @column.dateTime()
  declare trialStart: DateTime | null

  @column.dateTime()
  declare trialEnd: DateTime | null

  @column.dateTime()
  declare cancelledAt: DateTime | null

  @column()
  declare cancelAtPeriodEnd: boolean

  @column()
  declare maxUsers: number | null

  @column()
  declare maxStorage: number | null // Storage in GB

  @column()
  declare features: string | null // JSON string of enabled features

  @column()
  declare needsUpgrade: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @belongsTo(() => Tenant)
  declare tenant: BelongsTo<typeof Tenant>

  @belongsTo(() => PricingPlan, {
    foreignKey: 'plan',
    localKey: 'slug',
  })
  declare pricingPlan: BelongsTo<typeof PricingPlan>

  // Helper methods
  get isActive(): boolean {
    return this.status === BillingStatus.ACTIVE
  }

  get isTrialing(): boolean {
    if (!this.trialEnd) return false
    return DateTime.now() < this.trialEnd
  }

  get isPastDue(): boolean {
    return this.status === BillingStatus.PAST_DUE
  }

  get isCancelled(): boolean {
    return this.status === BillingStatus.CANCELLED
  }

  get daysUntilTrialEnd(): number | null {
    if (!this.trialEnd) return null
    const diff = this.trialEnd.diff(DateTime.now(), 'days')
    return Math.max(0, Math.floor(diff.days))
  }

  get daysUntilPeriodEnd(): number | null {
    if (!this.currentPeriodEnd) return null
    const diff = this.currentPeriodEnd.diff(DateTime.now(), 'days')
    return Math.max(0, Math.floor(diff.days))
  }

  get featuresArray(): string[] {
    if (!this.features) return []
    try {
      return JSON.parse(this.features)
    } catch {
      return []
    }
  }

  set featuresArray(features: string[]) {
    this.features = JSON.stringify(features)
  }

  hasFeature(feature: string): boolean {
    return this.featuresArray.includes(feature)
  }

  // Plan-specific feature checks
  get canCreateUnlimitedUsers(): boolean {
    return this.plan === BillingPlan.ENTERPRISE || this.maxUsers === null
  }

  get canUseAdvancedAnalytics(): boolean {
    return [BillingPlan.PRO, BillingPlan.ENTERPRISE].includes(this.plan)
  }

  get canUseCustomBranding(): boolean {
    return this.plan === BillingPlan.ENTERPRISE
  }

  get canUseAPIAccess(): boolean {
    return [BillingPlan.PRO, BillingPlan.ENTERPRISE].includes(this.plan)
  }
}
