import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import BaseController from '#controllers/base_controller'
import UserRoleService from '#services/user_role_service'
import { createUserValidator, updateUserValidator } from '#validators/user'
import UserService from '#services/user_service'

import EmailService from '#services/email_service'
import { hasPermission } from '#decorators/has_permission'
import inviteUserValidator from '#validators/invite_user'
import Tenant from '#models/tenant'

@inject()
export default class UsersController extends BaseController {
  constructor(
    protected userService: UserService,
    protected userRoleService: UserRoleService,
    protected emailService: EmailService
  ) {
    super()
  }

  /**
   * GET /users - Get list of users with pagination and search
   */
  @hasPermission('users.read')
  async index({ request, response }: HttpContext) {
    const filters = this.extractFilters(request, ['search', 'status'])
    const pagination = this.getPaginationParams(request)

    // Add tenant filter to the filters
    const tenantFilters = { ...filters }
    const data = await this.userService.getPaginated(
      { ...tenantFilters, ...pagination },
      request.tenant.id
    )

    // Enhance user data with role information
    const enhancedUsers = await Promise.all(
      data.data.map(async (user) => {
        const rbacData = await this.userRoleService.getUserRolesAndPermissions(
          user,
          request.tenant.id
        )
        return {
          ...user.toJSON(),
          ...rbacData,
        }
      })
    )

    return this.success(
      response,
      {
        data: enhancedUsers,
        pagination: data.pagination,
      },
      'Users retrieved successfully'
    )
  }

  /**
   * GET /users/:id - Get a single user by ID
   */
  @hasPermission('users.read')
  async show({ request, params, response }: HttpContext) {
    try {
      const user = await this.userService.findById(params.id, request.tenant.id)

      if (!this.validateResourceExists(user, response, 'User not found')) {
        return
      }

      // Get user roles and permissions for this tenant
      const rbacData = await this.userRoleService.getUserRolesAndPermissions(
        user,
        request.tenant.id
      )

      return this.success(
        response,
        {
          user: {
            ...user.toJSON(),
            ...rbacData,
          },
        },
        'User retrieved successfully'
      )
    } catch (error) {
      return this.handleError(response, error as Error, 'Failed to retrieve user')
    }
  }

  /**
   * POST /users - Create a new user
   */
  @hasPermission('users.create')
  async store({ request, response }: HttpContext) {
    try {
      const payload = await request.validateUsing(createUserValidator)

      const user = await this.userService.createUserWithRole({
        ...payload,
        tenantId: request.tenant.id,
      })

      return this.success(response, { user: user.toJSON() }, 'User created successfully', {}, 201)
    } catch (error) {
      return this.handleError(response, error as Error, 'Failed to create user')
    }
  }

  /**
   * POST /users/invite - Invite a new user
   */
  @hasPermission('users.create')
  async invite({ request, response }: HttpContext) {
    try {
      const payload = await request.validateUsing(inviteUserValidator)

      const { user, temporaryPassword } = await this.userService.invite({
        email: payload.email,
        roleId: payload.roleId,
        tenantId: request.tenant.id,
        permissions: payload.permissions,
      })

      // assign role and permission
      this.userService.assignRoleAndPermissions(
        user,
        payload.roleId,
        request.tenant.id,
        payload.permissions
      )

      const tenant = await Tenant.findOrFail(request.tenant.id)
      await this.emailService.sendUserInvitation(user, tenant, temporaryPassword)

      return this.success(response, user, 'User invited successfully')
    } catch (error) {
      return this.handleError(response, error as Error, 'Failed to invite user')
    }
  }

  /**
   * DELETE /users/:id - Delete a user from tenant
   */
  @hasPermission('users.delete')
  async destroy({ params, request, response }: HttpContext) {
    try {
      await this.userService.removeUserFromTenant(parseInt(params.id), request.tenant.id)

      return this.success(response, null, 'User removed from tenant successfully')
    } catch (error) {
      return this.handleError(response, error as Error, 'Failed to remove user from tenant')
    }
  }

  /**
   * PUT /users/:id/permissions - Update user permissions
   */
  @hasPermission('users.manage')
  async updatePermissions({ params, request, response }: HttpContext) {
    try {
      const { permissionIds } = request.only(['permissionIds'])
      const tenantId = request.tenant?.id || null

      if (!Array.isArray(permissionIds)) {
        return this.error(response, 'Permission IDs must be an array', 'Invalid input', 400)
      }

      await this.userService.updateUserPermissions({
        userId: parseInt(params.id),
        permissionIds,
        tenantId,
      })

      return this.success(response, null, 'User permissions updated successfully')
    } catch (error) {
      return this.handleError(response, error as Error, 'Failed to update user permissions')
    }
  }

  /**
   * GET /users/:id/permissions - Get user permissions
   */
  @hasPermission('users.read')
  async getPermissions({ params, request, response }: HttpContext) {
    try {
      const tenantId = request.tenant?.id || null
      const permissionNames = await this.userService.getUserPermissions(
        parseInt(params.id),
        tenantId
      )

      return this.success(
        response,
        { permissions: permissionNames },
        'User permissions retrieved successfully'
      )
    } catch (error) {
      return this.handleError(response, error as Error, 'Failed to retrieve user permissions')
    }
  }
}
